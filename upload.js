const { db } = require('./functions/config/firebaseConfig.js');
const xlsx = require("xlsx");
const fs = require("fs");

// Excel dosyasını oku
const workbook = xlsx.readFile("Lecturer Ratings (Responses).xlsx");
const sheetName = workbook.SheetNames[0]; // İlk sayfayı al
const sheet = workbook.Sheets[sheetName];

// Excel verisini JSON'a çevir
const data = xlsx.utils.sheet_to_json(sheet);

// Firestore'a yükle
async function uploadData() {
  const collectionRef = db.collection("teacherRatings");

  for (let record of data) {
    // Remove unnecessary fields
    delete record["Column 10"];

    // Set current timestamp
    // record["timestamp"] = new Date().toISOString();
    if (record["Timestamp"]) {
      delete record["Timestamp"];
    }

    // Firestore'a ekle
    await collectionRef.add(record);
    console.log("Veri eklendi:", record);
  }

  console.log("Tüm veriler başarıyla Firestore'a yüklendi!");
}

uploadData().catch(console.error);
