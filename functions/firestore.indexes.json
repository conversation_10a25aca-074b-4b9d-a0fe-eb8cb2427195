{"indexes": [{"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "toUserId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "toUserId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "toUserId", "order": "ASCENDING"}, {"fieldPath": "fromUserId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "questionId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}]}], "fieldOverrides": []}