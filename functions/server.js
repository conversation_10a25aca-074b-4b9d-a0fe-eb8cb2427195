const functions = require("firebase-functions");
const express = require("express");
const cors = require("cors");

const authRoutes = require("./routes/authRoutes");
const questionRoutes = require("./routes/questionRoutes");
const mealRoutes = require("./routes/mealRoutes");
const lecturerRoutes = require("./routes/lecturerRoutes");
const userRoutes = require("./routes/userRoutes");
const notificationRoutes = require("./routes/notificationRoutes");

const app = express();

// CORS configuration
app.use(
  cors({
    origin: [
      "http://127.0.0.1:3000",
      "http://127.0.0.1:8081",
      "https://metuhub-ca9e8.web.app",
      "https://metuhub.com",
    ],
    methods: ["GET", "POST", "PUT"],
    credentials: true,
  })
);

app.use(express.json());

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/posts", questionRoutes);
app.use("/api/meal", mealRoutes);
app.use("/api/lecturers", lecturerRoutes);
app.use("/api/users", userRoutes);
app.use("/api/notifications", notificationRoutes);

// Basic route for testing
app.get("/", (req, res) => {
  res.json({ message: "MetuHub API is running (Firebase Functions)" });
});

// Error handling
app.use((req, res) => {
  res.status(404).json({ error: "Route not found" });
});

if (require.main === module) {
  const PORT = 4000;
  app.listen(PORT, () => {
    console.log(`Server is running locally on port ${PORT}`);
  });
}

exports.api = functions.https.onRequest(app);
