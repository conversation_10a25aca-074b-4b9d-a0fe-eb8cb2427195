const express = require('express');
const {
    getUserProfile,
    getUsernameById,
    getUserProfileById,
    followUser,
    unfollowUser,
    getUserFollowers,
    getUserFollowing
} = require('../controllers/userController');

const router = express.Router();

// User profile routes
router.get('/profile', getUserProfile);
router.get('/username/:userId', getUsernameById);
router.get('/profile/:userId', getUserProfileById);

// Follow/unfollow routes
router.post('/follow/:targetUserId', followUser);
router.post('/unfollow/:targetUserId', unfollowUser);

// Followers/following routes
router.get('/followers/:userId', getUserFollowers);
router.get('/followings/:userId', getUserFollowing);

module.exports = router;