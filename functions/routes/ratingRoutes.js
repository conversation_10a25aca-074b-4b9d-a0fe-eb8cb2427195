const express = require("express");
const { addRating, getRating } = require("../controllers/ratingController");
const { mealScraper } = require("../controllers/mealController");

const router = express.Router();

router.post("/add-rating", addRating);
router.get("/get-rating", getRating);
router.post("/add-meals", mealScraper);

//! Bunlar hiçbir yerde kullanılmıyor gibi görünüyor. Ya da belki iki kere api oluşturulmuş olabilir.

module.exports = router;
