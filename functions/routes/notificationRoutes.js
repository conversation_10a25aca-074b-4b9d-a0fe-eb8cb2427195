const express = require('express');
const router = express.Router();
const { 
    getUserNotifications, 
    markNotificationAsRead, 
    markAllNotificationsAsRead 
} = require('../controllers/notificationController');

// Get all notifications for the authenticated user
router.get('/', getUserNotifications);

// Mark a specific notification as read
router.put('/:notificationId/read', markNotificationAsRead);

// Mark all notifications as read
router.put('/read-all', markAllNotificationsAsRead);

module.exports = router;
