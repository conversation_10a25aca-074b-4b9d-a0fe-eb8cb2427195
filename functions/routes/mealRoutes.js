const express = require('express');
const router = express.Router();
const { db } = require('../config/firebaseConfig');
const { mealScraper } = require('../controllers/mealController');

// Get all meals
router.get('/meals', async (req, res) => {
    try {
        const mealDoc = await db.collection('meals').doc('cafeteria-meals').get();

        if (!mealDoc.exists) {
            return res.status(404).json({ error: 'Meal document not found' });
        }

        res.status(200).json(mealDoc.data());
    } catch (error) {
        console.error('Error fetching meals:', error);
        res.status(500).json({ error: 'Failed to fetch meals' });
    }
});

// Yardımcı fonksiyon: <PERSON><PERSON><PERSON><PERSON><PERSON>n tarihi (DD-MM-YYYY)
// !! Başka dosyaya koymalısın.
function getTodayDateString() {
    const today = new Date();
    const dd = String(today.getDate()).padStart(2, '0');
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const yyyy = today.getFullYear();
    return `${dd}-${mm}-${yyyy}`;
}

// Add Rating endpoint
router.post('/addRating', async (req, res) => {
    const { rating, userId } = req.body;

    if (!rating || !userId) {
        return res.status(400).json({ success: false, message: 'rating ve userId gerekli.' });
    }

    const dateStr = getTodayDateString();
    const docRef = db.collection('daily-ratings').doc(dateStr);

    try {
        await db.runTransaction(async (t) => {
            const doc = await t.get(docRef);

            let data = {
                count: {},
                users: []
            };

            if (doc.exists) {
                data = doc.data();

                // Kullanıcı zaten oy vermiş mi kontrolü
                if (data.users && data.users.includes(userId)) {
                    throw new Error("Bu kullanıcı zaten oy vermiş.");
                }
            }

            // Oy sayısını artır
            const currentCount = data.count?.[rating] || 0;
            data.count = {
                ...data.count,
                [rating]: currentCount + 1
            };

            // Kullanıcıyı ekle
            data.users = [...(data.users || []), userId];

            // Firestore'a yaz
            t.set(docRef, data);
        });

        res.status(200).json({ success: true });
    } catch (error) {
        console.error('Rating eklenemedi:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// Trigger meal scraping
router.post('/scrape-meals', async (req, res) => {
    try {
        const meals = await mealScraper();
        res.status(200).json({ 
            message: 'Meals scraped successfully',
            meals: meals 
        });
    } catch (error) {
        console.error('Error scraping meals:', error);
        res.status(500).json({ error: 'Failed to scrape meals' });
    }
});

module.exports = router;