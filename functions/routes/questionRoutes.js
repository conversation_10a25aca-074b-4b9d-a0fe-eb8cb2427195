const express = require('express');
const router = express.Router();
const { createQuestion, getQuestions, getQuestionsByUserId } = require('../controllers/questionController');
const replyRoutes = require('./replyRoutes');

// Create a new question
router.post('/post', createQuestion);

// Get all questions
router.get('/get', getQuestions);

// Get questions by user ID
router.get('/:userId', getQuestionsByUserId);

// Replies routes - this will handle /api/posts/:id/replies
router.use('/:postId/replies', replyRoutes);

module.exports = router;