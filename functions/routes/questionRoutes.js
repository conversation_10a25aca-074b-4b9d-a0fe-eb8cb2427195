const express = require('express');
const router = express.Router();
const { createQuestion, getQuestions, getQuestionsBasic, getQuestionsCursor, getQuestionsByUserId } = require('../controllers/questionController');
const replyRoutes = require('./replyRoutes');

// Create a new question
router.post('/post', createQuestion);

// Get all questions with pagination (scalable version)
router.get('/', getQuestions);

// Get all questions with basic pagination (for small datasets)
router.get('/basic', getQuestionsBasic);

// Get all questions with cursor-based pagination (most scalable)
router.get('/cursor', getQuestionsCursor);

// Legacy endpoint (keeping for backward compatibility)
router.get('/get', getQuestions);

// Get questions by user ID
router.get('/:userId', getQuestionsByUserId);

// Replies routes - this will handle /api/posts/:id/replies
router.use('/:postId/replies', replyRoutes);

module.exports = router;