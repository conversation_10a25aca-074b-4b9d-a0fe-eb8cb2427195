
const express = require('express');
const router = express.Router();
const { db } = require('../config/firebaseConfig');


router.get('/lecturerRating', async (req, res) => {
    try {
        const snapshot = await db.collection('teacherRatings').orderBy('courseCode', 'asc').get();
        const lecturers = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
        }));
        res.json(lecturers);
    } catch (error) {
        console.error('Error fetching lecturer ratings:', error);
        res.status(500).json({ error: 'Failed to fetch lecturer ratings' });
    }
});

module.exports = router;
