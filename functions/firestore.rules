rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Default rule - deny all access
    match /{document=**} {
      allow read, write: if false;
    }

    // Users collection
    match /users/{userId} {
      // Allow read access to all users
      allow read: if true;

      // Allow write access only to the authenticated user for their own document
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Questions collection
    match /questions/{questionId} {
      // Allow read access to all users
      allow read: if true;

      // Allow write access only to authenticated users
      allow create: if request.auth != null;

      // Allow update/delete only to the question author
      allow update, delete: if request.auth != null &&
                             request.auth.uid == resource.data.userId;
    }

    // Replies collection
    match /replies/{replyId} {
      // Allow read access to all users
      allow read: if true;

      // Allow write access only to authenticated users
      allow create: if request.auth != null;

      // Allow update/delete only to the reply author
      allow update, delete: if request.auth != null &&
                             request.auth.uid == resource.data.userId;
    }

    // Notifications collection
    match /notifications/{notificationId} {
      // Allow read access only to the notification recipient
      allow read: if request.auth != null &&
                   request.auth.uid == resource.data.toUserId;

      // Allow create access to the server only (via Cloud Functions)
      allow create: if false; // Only allow creation via backend

      // Allow update only for marking as read by the recipient
      allow update: if request.auth != null &&
                     request.auth.uid == resource.data.toUserId &&
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isRead']);

      // Allow delete only to the notification recipient
      allow delete: if request.auth != null &&
                     request.auth.uid == resource.data.toUserId;
    }
  }
}