{"name": "metuhub-backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "nodemon server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "nodeman": "^1.1.2", "puppeteer": "^23.11.1"}, "devDependencies": {"nodemon": "^3.1.9"}}