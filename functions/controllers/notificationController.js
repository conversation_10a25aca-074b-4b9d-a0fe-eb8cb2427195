const { admin, db } = require('../config/firebaseConfig');

/**
 * Create a notification
 * @param {string} toUserId - User ID of the recipient
 * @param {string} fromUserId - User ID of the sender
 * @param {string} type - Type of notification: "reply", "mention", or "followedUserPost"
 * @param {string} questionId - ID of the related post or comment
 * @returns {Promise<string>} - ID of the created notification
 */
const createNotification = async (toUserId, fromUserId, type, questionId) => {
    try {
        // Don't notify users about their own actions
        if (toUserId === fromUserId) {
            return null;
        }

        // Check if a similar notification already exists (to prevent duplicates)
        const existingNotifications = await db.collection('notifications')
            .where('toUserId', '==', toUserId)
            .where('fromUserId', '==', fromUserId)
            .where('type', '==', type)
            .where('questionId', '==', questionId)
            .where('isRead', '==', false)
            .get();

        if (!existingNotifications.empty) {
            // Similar unread notification exists, don't create a duplicate
            return existingNotifications.docs[0].id;
        }

        // Create new notification
        const notificationRef = await db.collection('notifications').add({
            toUserId,
            fromUserId,
            type,
            questionId,
            isRead: false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });

        return notificationRef.id;
    } catch (error) {
        console.error('Error creating notification:', error);
        throw error;
    }
};

/**
 * Create a notification for a reply
 */
const createReplyNotification = async (postAuthorId, replyAuthorId, postId) => {
    return createNotification(postAuthorId, replyAuthorId, 'reply', postId);
};

/**
 * Create notifications for mentions in content
 */
const createMentionNotifications = async (content, authorId, postId) => {
    try {
        // Extract mentions from content (format: @username)
        const mentionRegex = /@(\w+)/g;
        const mentions = content.match(mentionRegex) || [];
        
        // Remove duplicates
        const uniqueMentions = [...new Set(mentions)].map(mention => mention.substring(1));
        
        const notificationPromises = [];
        
        for (const username of uniqueMentions) {
            // Find user by username
            const userSnapshot = await db.collection('users')
                .where('username', '==', username)
                .limit(1)
                .get();
                
            if (!userSnapshot.empty) {
                const userId = userSnapshot.docs[0].id;
                // Create notification for the mentioned user
                notificationPromises.push(createNotification(userId, authorId, 'mention', postId));
            }
        }
        
        await Promise.all(notificationPromises);
    } catch (error) {
        console.error('Error creating mention notifications:', error);
        throw error;
    }
};

/**
 * Create notifications for followers when a user creates a post
 */
const createFollowedUserPostNotifications = async (authorId, postId) => {
    try {
        // Find all users who follow the author
        const usersSnapshot = await db.collection('users')
            .where('following', 'array-contains', authorId)
            .get();
            
        const notificationPromises = [];
        
        usersSnapshot.forEach(doc => {
            const followerId = doc.id;
            notificationPromises.push(createNotification(followerId, authorId, 'followedUserPost', postId));
        });
        
        await Promise.all(notificationPromises);
    } catch (error) {
        console.error('Error creating followed user post notifications:', error);
        throw error;
    }
};

/**
 * Get notifications for a user
 */
const getUserNotifications = async (req, res) => {
    const authHeader = req.headers.authorization || '';
    const idToken = authHeader.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

    if (!idToken) {
        return res.status(401).json({ error: "Authentication required" });
    }

    try {
        // Verify the token and get the user ID
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        const userId = decodedToken.uid;
        
        // Get notifications for the user, ordered by creation time (newest first)
        const notificationsSnapshot = await db.collection('notifications')
            .where('toUserId', '==', userId)
            .orderBy('createdAt', 'desc')
            .get();
            
        const notifications = [];
        
        // Process each notification and add user details
        const userDetailsPromises = [];
        
        notificationsSnapshot.forEach(doc => {
            const notification = {
                id: doc.id,
                ...doc.data()
            };
            
            // Convert Firestore timestamp to regular timestamp
            if (notification.createdAt) {
                notification.createdAt = notification.createdAt.toDate();
            }
            
            notifications.push(notification);
            
            // Get user details for the sender
            userDetailsPromises.push(
                db.collection('users').doc(notification.fromUserId).get()
            );
        });
        
        // Get all user details in parallel
        const userDetailsSnapshots = await Promise.all(userDetailsPromises);
        
        // Add user details to notifications
        for (let i = 0; i < notifications.length; i++) {
            const userDoc = userDetailsSnapshots[i];
            if (userDoc.exists) {
                const userData = userDoc.data();
                notifications[i].fromUser = {
                    username: userData.username || '',
                    // Add other user fields as needed
                };
            }
        }
        
        return res.status(200).json(notifications);
    } catch (error) {
        console.error('Error getting notifications:', error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * Mark a notification as read
 */
const markNotificationAsRead = async (req, res) => {
    const authHeader = req.headers.authorization || '';
    const idToken = authHeader.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

    if (!idToken) {
        return res.status(401).json({ error: "Authentication required" });
    }

    try {
        // Verify the token and get the user ID
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        const userId = decodedToken.uid;
        
        const { notificationId } = req.params;
        
        if (!notificationId) {
            return res.status(400).json({ error: "Notification ID is required" });
        }
        
        // Get the notification
        const notificationDoc = await db.collection('notifications').doc(notificationId).get();
        
        if (!notificationDoc.exists) {
            return res.status(404).json({ error: "Notification not found" });
        }
        
        const notification = notificationDoc.data();
        
        // Check if the notification belongs to the user
        if (notification.toUserId !== userId) {
            return res.status(403).json({ error: "You don't have permission to mark this notification as read" });
        }
        
        // Mark as read
        await db.collection('notifications').doc(notificationId).update({
            isRead: true
        });
        
        return res.status(200).json({ message: "Notification marked as read" });
    } catch (error) {
        console.error('Error marking notification as read:', error);
        return res.status(500).json({ error: error.message });
    }
};

/**
 * Mark all notifications as read for a user
 */
const markAllNotificationsAsRead = async (req, res) => {
    const authHeader = req.headers.authorization || '';
    const idToken = authHeader.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

    if (!idToken) {
        return res.status(401).json({ error: "Authentication required" });
    }

    try {
        // Verify the token and get the user ID
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        const userId = decodedToken.uid;
        
        // Get all unread notifications for the user
        const notificationsSnapshot = await db.collection('notifications')
            .where('toUserId', '==', userId)
            .where('isRead', '==', false)
            .get();
            
        // Update all notifications in batch
        const batch = db.batch();
        
        notificationsSnapshot.forEach(doc => {
            batch.update(doc.ref, { isRead: true });
        });
        
        await batch.commit();
        
        return res.status(200).json({ 
            message: "All notifications marked as read",
            count: notificationsSnapshot.size
        });
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        return res.status(500).json({ error: error.message });
    }
};

module.exports = {
    createNotification,
    createReplyNotification,
    createMentionNotifications,
    createFollowedUserPostNotifications,
    getUserNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead
};
