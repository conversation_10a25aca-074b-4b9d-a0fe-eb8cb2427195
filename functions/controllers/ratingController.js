const { db } = require("../config/firebaseConfig");

async function addRating(req, res) {
  try {
    const { rating, user } = req.body;
    
    // Kullanıcıdan gelen verileri kontrol et
    if (!rating || !user) {
      return res.status(400).json({ error: "Rating and user are required." });
    }

    // Tarihi formatla (örnek: YYYY-MM-DD)
    const date = formatDate(new Date());

    // Firestore referansı oluştur
    const ratingRef = db.collection("daily-ratings").doc(date);
    const ratingSnapshot = await ratingRef.get();

    if (ratingSnapshot.exists) {
      // Mevcut dokümandan veriyi al
      const data = ratingSnapshot.data();
      const countData = data.count || {};
      const users = data.users || [];

      // Kullanıcının o günü zaten puanlayıp puanlamadığını kontrol et
      if (users.includes(user)) {
        return res.status(400).json({ error: "You already rated today." });
      }

      // Yeni kullanıcıyı ve oyu güncelle
      const updatedUsers = [...users, user];
      const updatedCount = {
        ...countData,
        [rating]: (countData[rating] || 0) + 1, // Oy sayısını artır
      };

      // Güncellenmiş veriyi oluştur
      const updatedRatingJson = {
        count: updatedCount,
        users: updatedUsers,
      };

      console.log("Updated Data:", updatedRatingJson);

      // Firestore'a yaz
      await ratingRef.set(updatedRatingJson, { merge: true });

      res.status(200).send({ success: "The data saved successfully!" });
    } else {
      // Yeni bir doküman oluştur
      const defaultRatingJson = {
        count: { [rating]: 1 },
        users: [user],
      };

      console.log("Default Data:", defaultRatingJson);

      await ratingRef.set(defaultRatingJson);
      res.status(200).send({ success: "The data saved successfully!" });
    }
  } catch (error) {
    // Hata durumunda konsola yazdır ve kullanıcıya döndür
    console.error("Error in addRating:", error);
    res.status(500).send({ error: error.message });
  }
}

async function getRating(req, res) {
  try {
    const date = new Date();
    const formattedDate = formatDate(date);

    console.log(formattedDate);

    const docRef = db.collection("daily-ratings").doc(formattedDate);
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      return res.status(404).json({ error: "The document can not found." });
    }

    const data = docSnapshot.data();
    // count is an map object.
    const count = data.count || {};
    // count is contains the rating and the count of the rating. for example: { 1: 2, 2: 3, 3: 1 }
    // return the rating map object.
    const rating = Object.keys(count).reduce((acc, key) => {
      return { ...acc, [key]: count[key] };
    }, {});

    res.status(200).json({ rating: rating });
  } catch (error) {
    console.error(error);
    res.status(500).send({ error: error.message });
  }
}

function formatDate(date) {
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
}

module.exports = {
  addRating,
  getRating,
};
