const { admin, db } = require('../config/firebaseConfig');


const getUserProfile = async (req, res) => {
  const authHeader = req.headers.authorization || '';
  const idToken = authHeader.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

  if (!idToken) {
      return res.status(401).json({ error: "No token provided" });
  }

  try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      const uid = decodedToken.uid;

      const userDoc = await db.collection('users').doc(uid).get();

      if (!userDoc.exists) {
          return res.status(404).json({ error: "User not found" });
      }

      const userData = userDoc.data();

      const userProfile = {
          uid: uid,
          username: userData.username || "",
          email: userData.email || "",
          isVerified: userData.isVerified || false,
          createdAt: userData.createdAt || null,
          followers: userData.followers || [],
          following: userData.following || []
      };

      return res.status(200).json(userProfile);
  } catch (error) {
      console.error("Error fetching user profile:", error);
      return res.status(401).json({ error: error.message || "Unauthorized" });
  }
};

// Get username by userId
const getUsernameById = async (req, res) => {
    try {
        const { userId } = req.params;

        if (!userId) {
            return res.status(400).json({ error: "User ID is required" });
        }

        const userDoc = await db.collection('users').doc(userId).get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: "User not found" });
        }

        const userData = userDoc.data();

        // Return just the username
        return res.status(200).json({
            userId: userId,
            username: userData.username || ""
        });
    } catch (error) {
        console.error("Error fetching username:", error);
        return res.status(500).json({ error: error.message || "Server error" });
    }
};

// Get user profile by userId
const getUserProfileById = async (req, res) => {
    try {
        const { userId } = req.params;

        if (!userId) {
            return res.status(400).json({ error: "User ID is required" });
        }

        const userDoc = await db.collection('users').doc(userId).get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: "User not found" });
        }

        const userData = userDoc.data();

        // Return the user profile
        const userProfile = {
            username: userData.username,
            createdAt: userData.createdAt || null,
            //metuhub member since 2025 march
            questions: userData.questions || [],
            followers: userData.followers || [],
            following: userData.following || []
        };

        return res.status(200).json(userProfile);
    } catch (error) {
        console.error("Error fetching user profile by ID:", error);
        return res.status(500).json({ error: error.message || "Server error" });
    }
};

// Follow a user
const followUser = async (req, res) => {
    const authHeader = req.headers.authorization || '';
    const idToken = authHeader.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

    if (!idToken) {
        return res.status(401).json({ error: "Authentication required to follow a user" });
    }

    try {
        // Verify the token and get the current user's ID
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        const currentUserId = decodedToken.uid;

        // Get the target user ID from the request
        const { targetUserId } = req.params;

        if (!targetUserId) {
            return res.status(400).json({ error: "Target user ID is required" });
        }

        // Check if trying to follow self
        if (currentUserId === targetUserId) {
            return res.status(400).json({ error: "You cannot follow yourself" });
        }

        // Get both user documents
        const currentUserDoc = await db.collection('users').doc(currentUserId).get();
        const targetUserDoc = await db.collection('users').doc(targetUserId).get();

        if (!currentUserDoc.exists) {
            return res.status(404).json({ error: "Current user not found" });
        }

        if (!targetUserDoc.exists) {
            return res.status(404).json({ error: "Target user not found" });
        }

        // Get user data
        const currentUserData = currentUserDoc.data();

        // Initialize arrays if they don't exist
        const currentUserFollowing = currentUserData.following || [];

        // Check if already following
        if (currentUserFollowing.includes(targetUserId)) {
            return res.status(400).json({ error: "You are already following this user" });
        }

        // Update current user's following list
        await db.collection('users').doc(currentUserId).update({
            following: admin.firestore.FieldValue.arrayUnion(targetUserId)
        });

        // Update target user's followers list
        await db.collection('users').doc(targetUserId).update({
            followers: admin.firestore.FieldValue.arrayUnion(currentUserId)
        });

        return res.status(200).json({
            message: "Successfully followed user",
            targetUserId: targetUserId
        });
    } catch (error) {
        console.error("Error following user:", error);
        return res.status(500).json({ error: error.message || "Server error" });
    }
};

// Unfollow a user
const unfollowUser = async (req, res) => {
    const authHeader = req.headers.authorization || '';
    const idToken = authHeader.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

    if (!idToken) {
        return res.status(401).json({ error: "Authentication required to unfollow a user" });
    }

    try {
        // Verify the token and get the current user's ID
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        const currentUserId = decodedToken.uid;

        // Get the target user ID from the request
        const { targetUserId } = req.params;

        if (!targetUserId) {
            return res.status(400).json({ error: "Target user ID is required" });
        }

        // Get both user documents
        const currentUserDoc = await db.collection('users').doc(currentUserId).get();
        const targetUserDoc = await db.collection('users').doc(targetUserId).get();

        if (!currentUserDoc.exists) {
            return res.status(404).json({ error: "Current user not found" });
        }

        if (!targetUserDoc.exists) {
            return res.status(404).json({ error: "Target user not found" });
        }

        // Get user data
        const currentUserData = currentUserDoc.data();

        // Initialize arrays if they don't exist
        const currentUserFollowing = currentUserData.following || [];

        // Check if not following
        if (!currentUserFollowing.includes(targetUserId)) {
            return res.status(400).json({ error: "You are not following this user" });
        }

        // Update current user's following list
        await db.collection('users').doc(currentUserId).update({
            following: admin.firestore.FieldValue.arrayRemove(targetUserId)
        });

        // Update target user's followers list
        await db.collection('users').doc(targetUserId).update({
            followers: admin.firestore.FieldValue.arrayRemove(currentUserId)
        });

        return res.status(200).json({
            message: "Successfully unfollowed user",
            targetUserId: targetUserId
        });
    } catch (error) {
        console.error("Error unfollowing user:", error);
        return res.status(500).json({ error: error.message || "Server error" });
    }
};

// Get user's followers
const getUserFollowers = async (req, res) => {
    try {
        const { userId } = req.params;

        if (!userId) {
            return res.status(400).json({ error: "User ID is required" });
        }

        const userDoc = await db.collection('users').doc(userId).get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: "User not found" });
        }

        const userData = userDoc.data();
        const followers = userData.followers || [];

        // Get details for each follower
        const followersDetails = [];

        if (followers.length > 0) {
            const followersPromises = followers.map(async (followerId) => {
                const followerDoc = await db.collection('users').doc(followerId).get();
                if (followerDoc.exists) {
                    const followerData = followerDoc.data();
                    return {
                        userId: followerId,
                        username: followerData.username || ""
                    };
                }
                return null;
            });

            const results = await Promise.all(followersPromises);
            followersDetails.push(...results.filter(item => item !== null));
        }

        return res.status(200).json({ followers: followersDetails });
    } catch (error) {
        console.error("Error getting followers:", error);
        return res.status(500).json({ error: error.message || "Server error" });
    }
};

// Get users that a user is following
const getUserFollowing = async (req, res) => {
    try {
        const { userId } = req.params;

        if (!userId) {
            return res.status(400).json({ error: "User ID is required" });
        }

        const userDoc = await db.collection('users').doc(userId).get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: "User not found" });
        }

        const userData = userDoc.data();
        const following = userData.following || [];

        // Get details for each followed user
        const followingDetails = [];

        if (following.length > 0) {
            const followingPromises = following.map(async (followingId) => {
                const followingDoc = await db.collection('users').doc(followingId).get();
                if (followingDoc.exists) {
                    const followingData = followingDoc.data();
                    return {
                        userId: followingId,
                        username: followingData.username || ""
                    };
                }
                return null;
            });

            const results = await Promise.all(followingPromises);
            followingDetails.push(...results.filter(item => item !== null));
        }

        return res.status(200).json({ following: followingDetails });
    } catch (error) {
        console.error("Error getting following:", error);
        return res.status(500).json({ error: error.message || "Server error" });
    }
};

module.exports = {
    getUserProfile,
    getUsernameById,
    getUserProfileById,
    followUser,
    unfollowUser,
    getUserFollowers,
    getUserFollowing
};