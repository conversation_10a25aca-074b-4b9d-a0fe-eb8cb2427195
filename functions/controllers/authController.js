const { admin, db } = require("../config/firebaseConfig");
const nodemailer = require("nodemailer");
require('dotenv').config();

const handleEmailVerification = async (req, res) => {
    try {
        const { uid } = req.body;

        // UID ile kullanıcıyı al
        const userRecord = await admin.auth().getUser(uid);

        if (!userRecord.isVerified) {
            // Kullanıcının isVerified alanını true yap
            await admin.auth().updateUser(uid, {
                isVerified: true
            });
        }

        // Firestore'da users/$uid içindeki isVerified alanını true yap
        await db.collection('users').doc(uid).update({
            isVerified: true
        });

        // Get user data to include in response
        const userDoc = await db.collection('users').doc(uid).get();
        const userData = userDoc.data();

        // Get username from Firestore or Firebase Auth
        const username = userData.username || userRecord.displayName;

        // Username is obligatory, so this is a safety check
        if (!username) {
            console.error(`User ${uid} has no username during verification. This should not happen.`);
            // Update the user record with a temporary username if missing
            const tempUsername = `user_${uid.substring(0, 8)}`;
            await db.collection('users').doc(uid).update({
                username: tempUsername
            });

            return res.status(200).json({
                message: 'Email verified successfully',
                isVerified: true,
                username: tempUsername,
                notice: "Your account was missing a username and has been assigned a temporary one. Please update it in your profile."
            });
        }

        return res.status(200).json({
            message: 'Email verified successfully',
            isVerified: true,
            username: username
        });

    } catch (error) {
        console.error('Error handling email verification:', error);
        return res.status(500).json({ error: error.message });
    }
};

const sendVerificationEmail = async (email) => {
  try {
    const actionCodeSettings = {
      url: "http://metuhub-ca9e8.web.app/metuhub/src/auth/login.html",
      handleCodeInApp: true,
    };

    const verificationLink = await admin
      .auth()
      .generateEmailVerificationLink(email, actionCodeSettings);

    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_PASS,
      },
      debug: true,
    });

    // E-posta gönderimi
    const result = await transporter.sendMail({
      from: "<EMAIL>",
      to: email,
      subject: "Verify your email address",
      html: `<p>Merhaba,</p>
                  <p>Hesabınızı doğrulamak için <a href="${verificationLink}">buraya tıklayın</a>.</p>`,
    });

    console.log("E-posta gönderildi:", result);
    return result;
  } catch (error) {
    console.error("E-posta gönderme hatası:", error.message);
    throw error;
  }
};

const signup = async (req, res) => {
  try {
    const { email, password, username } = req.body;

    // Validate METU email
    if (!email.endsWith("@metu.edu.tr")) {
      return res
        .status(400)
        .json({ error: "Only METU email addresses are allowed" });
    }

    // Validate username - it's obligatory for the site
    if (!username) {
      return res
        .status(400)
        .json({ error: "Username is required and cannot be empty" });
    }

    // Validate username format and length
    if (username.length < 3 || username.length > 30) {
      return res
        .status(400)
        .json({ error: "Username must be between 3 and 30 characters" });
    }

    // Check for valid characters (letters, numbers, underscores, hyphens)
    const usernameRegex = /^[a-zA-Z0-9_-]+$/;
    if (!usernameRegex.test(username)) {
      return res
        .status(400)
        .json({ error: "Username can only contain letters, numbers, underscores, and hyphens" });
    }

    // Create user in Firebase Auth
    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
      displayName: username,
      emailVerified: false,
    });

    // Create user document in Firestore
    await db.collection("users").doc(userRecord.uid).set({
      isVerified: false,
      username: username,
      questions: {},
    });

    // Send verification email
    await sendVerificationEmail(email);

    return res.status(201).json({
      message:
        "We sent you a verification email. Please check your email to complete the registration process.",
      userId: userRecord.uid,
      username: username,
    });
  } catch (error) {
    console.error("Error creating user:", error);
    return res.status(500).json({ error: error.message });
  }
};

const login = async (req, res) => {
  const authHeader = req.headers.authorization || '';
  const idToken = authHeader.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

  if (!idToken) {
    return res.status(401).json({ error: "No token provided" });
  }

  try {
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    const uid = decodedToken.uid;
    const userRecord = await admin.auth().getUser(uid);

    // Get user data from Firestore to ensure we have the username
    const userDoc = await db.collection('users').doc(uid).get();
    const userData = userDoc.data();

    // Get username from Firestore or Firebase Auth
    const username = userData.username || userRecord.displayName;

    // Username is obligatory, so this is a safety check
    if (!username) {
      console.error(`User ${uid} has no username. This should not happen.`);
      // Update the user record with a temporary username if missing
      const tempUsername = `user_${uid.substring(0, 8)}`;
      await db.collection('users').doc(uid).update({
        username: tempUsername
      });

      return res.status(200).json({
        message: "Login successful",
        uid: uid,
        username: tempUsername,
        notice: "Your account was missing a username and has been assigned a temporary one. Please update it in your profile."
      });
    }

    return res.status(200).json({
      message: "Login successful",
      uid: uid,
      username: username,
    });
  } catch (error) {
    console.error("Login error:", error);
    return res.status(401).json({ error: "Unauthorized" });
  }
};

const signout = async (req, res) => {
  // In Firebase, signout is primarily handled on the client-side
  // This endpoint acknowledges the signout request and can perform any server-side cleanup if needed

  try {
    // Get the authorization header
    const authHeader = req.headers.authorization || '';
    const idToken = authHeader.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

    if (!idToken) {
      // If no token is provided, we can still return success as the user might already be signed out
      return res.status(200).json({ message: "Signout successful" });
    }

    try {
      // Verify the token to get the user ID
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      const uid = decodedToken.uid;

      // Log the signout event
      console.log(`User ${uid} signed out`);

      // Note: Firebase doesn't have a server-side signout method
      // The actual token invalidation happens on the client-side

      return res.status(200).json({ message: "Signout successful" });
    } catch (error) {
      // If token verification fails, the token is already invalid or expired
      // We can still consider this a successful signout
      return res.status(200).json({ message: "Signout successful" });
    }
  } catch (error) {
    console.error("Signout error:", error);
    return res.status(500).json({ error: "Server error during signout" });
  }
};


const sendForgotPasswordEmail = async (req, res) => {
  const { email } = req.body;

  if (!email || typeof email !== "string") {
    return res.status(400).json({ error: "Geçerli bir e-posta adresi girin." });
  }

  try {
    const actionCodeSettings = {
      url: "https://metuhub-ca9e8.web.app/pages/auth/reset-password",
      handleCodeInApp: true,
    };

    const resetLink = await admin.auth().generatePasswordResetLink(email, actionCodeSettings);

    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_PASS,
      },
    });

    const result = await transporter.sendMail({
      from: `"Metuhub" <${process.env.GMAIL_USER}>`,
      to: email,
      subject: "Password Reset Link",
      html: `
        <p>Hello,</p>
        <p>You have requested a password reset for your Metuhub account.</p>
        <p>Please click the following link to reset your password:</p>
        <p><a href="${resetLink}">Reset Password</a></p>
        <p>If you did not request this, you can ignore this email.</p>
      `,
    });

    console.log("Şifre sıfırlama e-postası gönderildi:", result);
    return res.status(200).json({ message: "Şifre sıfırlama e-postası gönderildi" });
  } catch (error) {
    console.error("Şifre sıfırlama e-postası hatası:", error.message);
    return res.status(500).json({ error: "E-posta gönderimi başarısız: " + error.message });
  }
};

module.exports = {
  signup,
  login,
  signout,
  sendVerificationEmail,
  handleEmailVerification,
  sendForgotPasswordEmail
};
