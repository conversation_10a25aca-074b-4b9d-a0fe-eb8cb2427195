const puppeteer = require('puppeteer');
const { db } = require('../config/firebaseConfig');

async function mealScraper() {
  const url = 'https://kafeterya.metu.edu.tr/';

  try {
    const browser = await puppeteer.launch({ headless: false });
    const page = await browser.newPage();

    await page.goto(url, { waitUntil: 'networkidle2' });

    // Yemek kutularının geldiğinden emin ol
    await page.waitForSelector('.col-xs-6.col-sm-3.col-md-3.p-0', { timeout: 5000 });

    const meals = await page.evaluate(() => {
      const mealData = {};

      const mealElements = document.querySelectorAll('.col-xs-6.col-sm-3.col-md-3.p-0');

      mealElements.forEach((meal) => {
        const titleElement = meal.querySelector('h2 a');
        const imageElement = meal.querySelector('img');

        const mealName = titleElement ? titleElement.textContent.trim() : null;
        const imageUrl = imageElement ? imageElement.src : null;

        if (mealName && imageUrl) {
          mealData[mealName] = imageUrl;
        }
      });

      return mealData;
    });

    console.log('Yemekler:', meals);

    await db.collection('meals').doc('cafeteria-meals').set({ meals });
  
    console.log('Veriler Firebase Firestore\'a başarıyla kaydedildi.');

    await browser.close();
    return meals;
  } catch (error) {
    console.error('Error:', error.message);
    return {};
  }
}

module.exports = { mealScraper };