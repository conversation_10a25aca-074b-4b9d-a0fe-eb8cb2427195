const { admin, db } = require('../config/firebaseConfig');
const { createReplyNotification, createMentionNotifications } = require('./notificationController');

const createReply = async (req, res) => {
    try {
        const { postId } = req.params;
        const { content, userId } = req.body;

        if (!content || !userId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Check if post exists
        const postRef = db.collection('questions').doc(postId);
        const postDoc = await postRef.get();

        if (!postDoc.exists) {
            return res.status(404).json({ error: 'Post not found' });
        }

        // Get post author ID for notification
        const postData = postDoc.data();
        const postAuthorId = postData.userId;

        // Check if user exists
        const userRef = db.collection('users').doc(userId);
        const userDoc = await userRef.get();

        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Create reply in replies collection
        const replyData = {
            postId,
            userId,
            content,
            createdAt: new Date()
        };

        const replyRef = await db.collection('replies').add(replyData);
        const replyId = replyRef.id;

        // Update the question document to include the reply ID in its replies array
        await postRef.update({
            replies: admin.firestore.FieldValue.arrayUnion(replyId)
        });

        // Create notification for the post author (if not the same as reply author)
        try {
            await createReplyNotification(postAuthorId, userId, postId);

            // Check for mentions in the reply content and create notifications
            await createMentionNotifications(content, userId, postId);
        } catch (notificationError) {
            // Log notification error but don't fail the reply creation
            console.error('Error creating notifications:', notificationError);
        }

        res.status(201).json({
            message: 'Reply added to post',
            replyId: replyId,
            reply: {
                ...replyData,
                id: replyId
            }
        });

    } catch (error) {
        console.error('Error adding reply:', error);
        res.status(500).json({ error: error.message });
    }
};

// Get all replies for a post
const getRepliesByPostId = async (req, res) => {
    try {
        const { postId } = req.params;

        if (!postId) {
            return res.status(400).json({ error: 'Post ID is required' });
        }

        // Step 1: Get the question document to access its replies array
        const questionDoc = await db.collection('questions').doc(postId).get();

        if (!questionDoc.exists) {
            return res.status(404).json({ error: 'Question not found' });
        }

        // Step 2: Get the array of reply IDs from the question
        const questionData = questionDoc.data();
        const replyIds = questionData.replies || [];

        if (replyIds.length === 0) {
            return res.status(200).json([]);
        }

        // Step 3: Fetch each reply from the replies collection using its ID
        const repliesData = await Promise.all(
            replyIds.map(async (replyId) => {
                const replyDoc = await db.collection('replies').doc(replyId).get();

                if (!replyDoc.exists) {
                    return null;
                }

                const replyData = replyDoc.data();

                // Step 4: Get username for each reply
                let username = 'Unknown User';
                if (replyData.userId) {
                    const userDoc = await db.collection('users').doc(replyData.userId).get();
                    if (userDoc.exists) {
                        username = userDoc.data().username || 'Unknown User';
                    }
                }

                return {
                    id: replyDoc.id,
                    ...replyData,
                    username
                };
            })
        );

        // Filter out any null values and sort by creation date
        const validReplies = repliesData
            .filter(reply => reply !== null)
            .sort((a, b) => {
                const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
                const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
                return dateA - dateB;
            });

        res.status(200).json(validReplies);

    } catch (error) {
        console.error('Error fetching replies:', error);
        res.status(500).json({ error: error.message });
    }
};

module.exports = {
    createReply,
    getRepliesByPostId
};
