const { admin, db } = require('../config/firebaseConfig');
const { createMentionNotifications, createFollowedUserPostNotifications } = require('./notificationController');

const createQuestion = async (req, res) => {
    try {
        const { title, body, tags, userId } = req.body;

        // Validate input
        if (!title || !body || !tags || !userId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        if (title.length > 150) {
            return res.status(400).json({ error: 'Title exceeds 150 characters' });
        }

        if (tags.length > 5) {
            return res.status(400).json({ error: 'Maximum 5 tags allowed' });
        }

        // Get user data
        const userDoc = await db.collection('users').doc(userId).get();
        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Create question
        const questionData = {
            userId,
            title,
            body,
            tags,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            replies: [] // Initialize empty replies array
        };

        const questionRef = await db.collection('questions').add(questionData);
        const questionId = questionRef.id;

        // Update user's questions array
        await db.collection('users').doc(userId).update({
            questions: admin.firestore.FieldValue.arrayUnion(questionId)
        });

        // Create notifications
        try {
            // Check for mentions in the post content and create notifications
            await createMentionNotifications(body, userId, questionId);

            // Create notifications for users who follow the post author
            await createFollowedUserPostNotifications(userId, questionId);
        } catch (notificationError) {
            // Log notification error but don't fail the post creation
            console.error('Error creating notifications:', notificationError);
        }

        res.status(201).json({
            message: 'Question created successfully',
            questionId: questionId
        });
    } catch (error) {
        console.error('Error creating question:', error);
        res.status(500).json({ error: error.message });
    }
};

const getQuestions = async (_, res) => {
    try {
        const questionsSnapshot = await db.collection('questions')
            .orderBy('createdAt', 'desc')
            .get();

        const questions = [];
        questionsSnapshot.forEach(doc => {
            const questionData = doc.data();
            questions.push({
                id: doc.id,
                ...questionData,
                replyCount: Array.isArray(questionData.replies) ? questionData.replies.length : 0
            });
        });

        res.status(200).json(questions);
    } catch (error) {
        console.error('Error fetching questions:', error);
        res.status(500).json({ error: error.message });
    }
};


const getQuestionsByUserId = async (req, res) => {
    try {
        const userId = req.params.userId;
        const { lastVisible, limit = 10 } = req.query;
        // If user have more than 10 posts, we will implement the design in frontend.
        // when our site is active.
        let postsQuery = db.collection('questions')
            .where('userId', '==', userId)
            .orderBy('createdAt', 'desc')
            .limit(Number(limit));

        if (lastVisible) {
            const lastDoc = await db.collection('posts').doc(lastVisible).get();
            if (lastDoc.exists) {
                postsQuery = postsQuery.startAfter(lastDoc);
            }
        }

        const querySnapshot = await postsQuery.get();

        const posts = [];
        querySnapshot.forEach((doc) => {
            const postData = doc.data();
            posts.push({
                id: doc.id,
                ...postData,
                replyCount: Array.isArray(postData.replies) ? postData.replies.length : 0
            });
        });

        const newLastVisible = querySnapshot.docs.length > 0
            ? querySnapshot.docs[querySnapshot.docs.length - 1].id
            : null;

        res.status(200).json({ posts, lastVisible: newLastVisible });
    } catch (error) {
        console.error('Error fetching posts:', error);
        res.status(500).json({ error: error.message });
    }
};

module.exports = {
    createQuestion,
    getQuestions,
    getQuestionsByUserId
};