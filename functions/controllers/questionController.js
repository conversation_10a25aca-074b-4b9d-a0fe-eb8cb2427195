const { admin, db } = require('../config/firebaseConfig');
const { createMentionNotifications, createFollowedUserPostNotifications } = require('./notificationController');

const createQuestion = async (req, res) => {
    try {
        const { title, body, tags, userId } = req.body;

        // Validate input
        if (!title || !body || !tags || !userId) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        if (title.length > 150) {
            return res.status(400).json({ error: 'Title exceeds 150 characters' });
        }

        if (tags.length > 5) {
            return res.status(400).json({ error: 'Maximum 5 tags allowed' });
        }

        // Get user data
        const userDoc = await db.collection('users').doc(userId).get();
        if (!userDoc.exists) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Create question
        const questionData = {
            userId,
            title,
            body,
            tags,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            replies: [] // Initialize empty replies array
        };

        const questionRef = await db.collection('questions').add(questionData);
        const questionId = questionRef.id;

        // Update user's questions array
        await db.collection('users').doc(userId).update({
            questions: admin.firestore.FieldValue.arrayUnion(questionId)
        });

        // Create notifications
        try {
            // Check for mentions in the post content and create notifications
            await createMentionNotifications(body, userId, questionId);

            // Create notifications for users who follow the post author
            await createFollowedUserPostNotifications(userId, questionId);
        } catch (notificationError) {
            // Log notification error but don't fail the post creation
            console.error('Error creating notifications:', notificationError);
        }

        res.status(201).json({
            message: 'Question created successfully',
            questionId: questionId
        });
    } catch (error) {
        console.error('Error creating question:', error);
        res.status(500).json({ error: error.message });
    }
};

// Basic pagination using get() + slice() - suitable for small datasets
const getQuestionsBasic = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 15;

        // Validate pagination parameters
        if (page < 1) {
            return res.status(400).json({ error: 'Page must be greater than 0' });
        }
        if (limit < 1 || limit > 100) {
            return res.status(400).json({ error: 'Limit must be between 1 and 100' });
        }

        const questionsSnapshot = await db.collection('questions')
            .orderBy('createdAt', 'desc')
            .get();

        const allQuestions = [];
        questionsSnapshot.forEach(doc => {
            const questionData = doc.data();
            allQuestions.push({
                id: doc.id,
                ...questionData,
                replyCount: Array.isArray(questionData.replies) ? questionData.replies.length : 0
            });
        });

        // Calculate pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedQuestions = allQuestions.slice(startIndex, endIndex);
        const hasMore = endIndex < allQuestions.length;

        res.status(200).json({
            posts: paginatedQuestions,
            hasMore: hasMore,
            totalCount: allQuestions.length,
            currentPage: page,
            totalPages: Math.ceil(allQuestions.length / limit)
        });
    } catch (error) {
        console.error('Error fetching questions:', error);
        res.status(500).json({ error: error.message });
    }
};

// Scalable pagination using startAfter() - suitable for large datasets
const getQuestions = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 15;

        // Validate pagination parameters
        if (page < 1) {
            return res.status(400).json({ error: 'Page must be greater than 0' });
        }
        if (limit < 1 || limit > 100) {
            return res.status(400).json({ error: 'Limit must be between 1 and 100' });
        }

        let query = db.collection('questions')
            .orderBy('createdAt', 'desc')
            .limit(limit + 1); // Get one extra to check if there are more

        // For pages beyond the first, we need to skip documents
        // This is a simplified approach - for production, consider using cursor-based pagination
        if (page > 1) {
            const skipCount = (page - 1) * limit;
            const skipQuery = db.collection('questions')
                .orderBy('createdAt', 'desc')
                .limit(skipCount);

            const skipSnapshot = await skipQuery.get();
            if (skipSnapshot.docs.length > 0) {
                const lastSkippedDoc = skipSnapshot.docs[skipSnapshot.docs.length - 1];
                query = query.startAfter(lastSkippedDoc);
            }
        }

        const questionsSnapshot = await query.get();
        const questions = [];

        // Process documents (excluding the extra one used for hasMore check)
        const docsToProcess = questionsSnapshot.docs.slice(0, limit);
        docsToProcess.forEach(doc => {
            const questionData = doc.data();
            questions.push({
                id: doc.id,
                ...questionData,
                replyCount: Array.isArray(questionData.replies) ? questionData.replies.length : 0
            });
        });

        // Check if there are more documents
        const hasMore = questionsSnapshot.docs.length > limit;

        res.status(200).json({
            posts: questions,
            hasMore: hasMore
        });
    } catch (error) {
        console.error('Error fetching questions:', error);
        res.status(500).json({ error: error.message });
    }
};

// Production-ready cursor-based pagination - most scalable approach
const getQuestionsCursor = async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 15;
        const cursor = req.query.cursor; // Document ID to start after

        // Validate limit
        if (limit < 1 || limit > 100) {
            return res.status(400).json({ error: 'Limit must be between 1 and 100' });
        }

        let query = db.collection('questions')
            .orderBy('createdAt', 'desc')
            .limit(limit + 1); // Get one extra to check if there are more

        // If cursor is provided, start after that document
        if (cursor) {
            try {
                const cursorDoc = await db.collection('questions').doc(cursor).get();
                if (cursorDoc.exists) {
                    query = query.startAfter(cursorDoc);
                } else {
                    return res.status(400).json({ error: 'Invalid cursor' });
                }
            } catch (error) {
                return res.status(400).json({ error: 'Invalid cursor format' });
            }
        }

        const questionsSnapshot = await query.get();
        const questions = [];

        // Process documents (excluding the extra one used for hasMore check)
        const docsToProcess = questionsSnapshot.docs.slice(0, limit);
        docsToProcess.forEach(doc => {
            const questionData = doc.data();
            questions.push({
                id: doc.id,
                ...questionData,
                replyCount: Array.isArray(questionData.replies) ? questionData.replies.length : 0
            });
        });

        // Check if there are more documents and get next cursor
        const hasMore = questionsSnapshot.docs.length > limit;
        const nextCursor = hasMore && questions.length > 0 ? questions[questions.length - 1].id : null;

        res.status(200).json({
            posts: questions,
            hasMore: hasMore,
            nextCursor: nextCursor
        });
    } catch (error) {
        console.error('Error fetching questions with cursor:', error);
        res.status(500).json({ error: error.message });
    }
};

const getQuestionsByUserId = async (req, res) => {
    try {
        const userId = req.params.userId;
        const { lastVisible, limit = 10 } = req.query;
        // If user have more than 10 posts, we will implement the design in frontend.
        // when our site is active.
        let postsQuery = db.collection('questions')
            .where('userId', '==', userId)
            .orderBy('createdAt', 'desc')
            .limit(Number(limit));

        if (lastVisible) {
            const lastDoc = await db.collection('questions').doc(lastVisible).get();
            if (lastDoc.exists) {
                postsQuery = postsQuery.startAfter(lastDoc);
            }
        }

        const querySnapshot = await postsQuery.get();

        const posts = [];
        querySnapshot.forEach((doc) => {
            const postData = doc.data();
            posts.push({
                id: doc.id,
                ...postData,
                replyCount: Array.isArray(postData.replies) ? postData.replies.length : 0
            });
        });

        const newLastVisible = querySnapshot.docs.length > 0
            ? querySnapshot.docs[querySnapshot.docs.length - 1].id
            : null;

        res.status(200).json({ posts, lastVisible: newLastVisible });
    } catch (error) {
        console.error('Error fetching posts:', error);
        res.status(500).json({ error: error.message });
    }
};

module.exports = {
    createQuestion,
    getQuestions,
    getQuestionsBasic,
    getQuestionsCursor,
    getQuestionsByUserId
};