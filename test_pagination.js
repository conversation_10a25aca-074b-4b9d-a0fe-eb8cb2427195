// Simple test script for pagination endpoints
// Run with: node test_pagination.js

const BASE_URL = 'http://localhost:4000'; // Adjust if your server runs on different port

async function testPaginationEndpoints() {
    console.log('🧪 Testing Pagination Endpoints\n');

    // Test 1: Basic Pagination
    console.log('1️⃣ Testing Basic Pagination (/api/posts/basic)');
    try {
        const response = await fetch(`${BASE_URL}/api/posts/basic?page=1&limit=5`);
        const data = await response.json();
        
        console.log(`   Status: ${response.status}`);
        console.log(`   Posts returned: ${data.posts?.length || 0}`);
        console.log(`   Has more: ${data.hasMore}`);
        console.log(`   Total count: ${data.totalCount}`);
        console.log(`   Current page: ${data.currentPage}`);
        console.log(`   Total pages: ${data.totalPages}\n`);
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}\n`);
    }

    // Test 2: Scalable Pagination
    console.log('2️⃣ Testing Scalable Pagination (/api/posts)');
    try {
        const response = await fetch(`${BASE_URL}/api/posts?page=1&limit=5`);
        const data = await response.json();
        
        console.log(`   Status: ${response.status}`);
        console.log(`   Posts returned: ${data.posts?.length || 0}`);
        console.log(`   Has more: ${data.hasMore}\n`);
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}\n`);
    }

    // Test 3: Cursor-based Pagination
    console.log('3️⃣ Testing Cursor-based Pagination (/api/posts/cursor)');
    try {
        const response = await fetch(`${BASE_URL}/api/posts/cursor?limit=3`);
        const data = await response.json();
        
        console.log(`   Status: ${response.status}`);
        console.log(`   Posts returned: ${data.posts?.length || 0}`);
        console.log(`   Has more: ${data.hasMore}`);
        console.log(`   Next cursor: ${data.nextCursor || 'null'}\n`);

        // Test next page if cursor exists
        if (data.nextCursor) {
            console.log('   🔄 Testing next page with cursor...');
            const nextResponse = await fetch(`${BASE_URL}/api/posts/cursor?limit=3&cursor=${data.nextCursor}`);
            const nextData = await nextResponse.json();
            
            console.log(`   Next page posts: ${nextData.posts?.length || 0}`);
            console.log(`   Next page has more: ${nextData.hasMore}\n`);
        }
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}\n`);
    }

    // Test 4: Error handling
    console.log('4️⃣ Testing Error Handling');
    try {
        const response = await fetch(`${BASE_URL}/api/posts/basic?page=0&limit=150`);
        const data = await response.json();
        
        console.log(`   Status: ${response.status}`);
        console.log(`   Error message: ${data.error}\n`);
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}\n`);
    }

    console.log('✅ Testing completed!');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
    console.log('❌ This test requires Node.js 18+ or install node-fetch');
    console.log('Alternative: Test manually using curl or Postman');
    console.log('\nExample curl commands:');
    console.log('curl "http://localhost:4000/api/posts/basic?page=1&limit=5"');
    console.log('curl "http://localhost:4000/api/posts?page=1&limit=5"');
    console.log('curl "http://localhost:4000/api/posts/cursor?limit=5"');
} else {
    testPaginationEndpoints();
}

// Export for manual testing
module.exports = { testPaginationEndpoints };
