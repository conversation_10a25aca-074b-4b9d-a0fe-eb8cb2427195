// Diagnostic script to check Firestore data structure
// Run this to identify issues with your questions collection

const { admin, db } = require('./functions/config/firebaseConfig');

async function diagnoseFirestoreData() {
    console.log('🔍 Diagnosing Firestore Data Structure...\n');

    try {
        // Test 1: Check if we can read the questions collection
        console.log('1️⃣ Testing basic collection access...');
        const questionsRef = db.collection('questions');
        const snapshot = await questionsRef.limit(5).get();
        
        console.log(`   ✅ Found ${snapshot.size} documents in questions collection\n`);

        // Test 2: Check data structure of existing documents
        console.log('2️⃣ Analyzing document structure...');
        let hasCreatedAt = 0;
        let missingCreatedAt = 0;
        let hasAllRequiredFields = 0;
        let missingFields = 0;

        snapshot.forEach(doc => {
            const data = doc.data();
            console.log(`   Document ID: ${doc.id}`);
            
            // Check required fields
            const requiredFields = ['userId', 'title', 'body', 'tags', 'createdAt', 'replies'];
            const presentFields = [];
            const missingFieldsList = [];

            requiredFields.forEach(field => {
                if (data.hasOwnProperty(field)) {
                    presentFields.push(field);
                } else {
                    missingFieldsList.push(field);
                }
            });

            console.log(`   Present fields: ${presentFields.join(', ')}`);
            if (missingFieldsList.length > 0) {
                console.log(`   ❌ Missing fields: ${missingFieldsList.join(', ')}`);
                missingFields++;
            } else {
                console.log(`   ✅ All required fields present`);
                hasAllRequiredFields++;
            }

            // Check createdAt specifically
            if (data.createdAt) {
                hasCreatedAt++;
                console.log(`   CreatedAt: ${data.createdAt.toDate ? data.createdAt.toDate() : data.createdAt}`);
            } else {
                missingCreatedAt++;
                console.log(`   ❌ Missing createdAt field`);
            }

            console.log(`   Title: ${data.title || 'N/A'}`);
            console.log(`   Tags: ${JSON.stringify(data.tags || [])}`);
            console.log(`   Replies: ${Array.isArray(data.replies) ? data.replies.length : 'Not an array'}\n`);
        });

        console.log('📊 Summary:');
        console.log(`   Documents with createdAt: ${hasCreatedAt}`);
        console.log(`   Documents missing createdAt: ${missingCreatedAt}`);
        console.log(`   Documents with all fields: ${hasAllRequiredFields}`);
        console.log(`   Documents missing fields: ${missingFields}\n`);

        // Test 3: Try ordering by createdAt
        console.log('3️⃣ Testing createdAt ordering...');
        try {
            const orderedSnapshot = await questionsRef
                .orderBy('createdAt', 'desc')
                .limit(3)
                .get();
            
            console.log(`   ✅ Successfully ordered by createdAt`);
            console.log(`   Retrieved ${orderedSnapshot.size} documents\n`);
        } catch (error) {
            console.log(`   ❌ Error ordering by createdAt: ${error.message}`);
            console.log(`   This might indicate missing Firestore index\n`);
        }

        // Test 4: Check for any documents without proper structure
        console.log('4️⃣ Checking for problematic documents...');
        const allDocs = await questionsRef.get();
        let problematicDocs = [];

        allDocs.forEach(doc => {
            const data = doc.data();
            if (!data.createdAt || !data.userId || !data.title || !data.body) {
                problematicDocs.push({
                    id: doc.id,
                    issues: {
                        noCreatedAt: !data.createdAt,
                        noUserId: !data.userId,
                        noTitle: !data.title,
                        noBody: !data.body,
                        noTags: !data.tags,
                        noReplies: !data.replies
                    }
                });
            }
        });

        if (problematicDocs.length > 0) {
            console.log(`   ❌ Found ${problematicDocs.length} problematic documents:`);
            problematicDocs.forEach(doc => {
                console.log(`   Document ${doc.id}:`);
                Object.entries(doc.issues).forEach(([issue, hasIssue]) => {
                    if (hasIssue) {
                        console.log(`     - ${issue}`);
                    }
                });
            });
        } else {
            console.log(`   ✅ All documents have proper structure`);
        }

    } catch (error) {
        console.error('❌ Error during diagnosis:', error.message);
        console.error('Full error:', error);
    }
}

// Function to fix common issues
async function fixCommonIssues() {
    console.log('\n🔧 Attempting to fix common issues...\n');

    try {
        const questionsRef = db.collection('questions');
        const snapshot = await questionsRef.get();
        
        let fixedCount = 0;
        const batch = db.batch();

        snapshot.forEach(doc => {
            const data = doc.data();
            const updates = {};
            let needsUpdate = false;

            // Fix missing createdAt
            if (!data.createdAt) {
                updates.createdAt = admin.firestore.FieldValue.serverTimestamp();
                needsUpdate = true;
                console.log(`   Adding createdAt to document ${doc.id}`);
            }

            // Fix missing replies array
            if (!data.replies || !Array.isArray(data.replies)) {
                updates.replies = [];
                needsUpdate = true;
                console.log(`   Adding replies array to document ${doc.id}`);
            }

            // Fix missing tags array
            if (!data.tags || !Array.isArray(data.tags)) {
                updates.tags = [];
                needsUpdate = true;
                console.log(`   Adding tags array to document ${doc.id}`);
            }

            if (needsUpdate) {
                batch.update(doc.ref, updates);
                fixedCount++;
            }
        });

        if (fixedCount > 0) {
            await batch.commit();
            console.log(`\n   ✅ Fixed ${fixedCount} documents`);
        } else {
            console.log(`\n   ✅ No documents needed fixing`);
        }

    } catch (error) {
        console.error('❌ Error fixing issues:', error.message);
    }
}

// Run diagnosis
if (require.main === module) {
    diagnoseFirestoreData()
        .then(() => {
            console.log('\n🤔 Would you like to fix common issues? (Run fixCommonIssues function)');
            // Uncomment the next line to automatically fix issues
            // return fixCommonIssues();
        })
        .then(() => {
            console.log('\n✅ Diagnosis complete!');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Fatal error:', error);
            process.exit(1);
        });
}

module.exports = { diagnoseFirestoreData, fixCommonIssues };
