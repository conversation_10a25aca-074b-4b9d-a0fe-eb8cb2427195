# 🔧 Firestore Questions Collection Troubleshooting Guide

Based on your Firebase screenshot, here are the most likely issues and solutions:

## 🚨 **Most Common Issues**

### 1. **Missing Firestore Index**
**Problem:** Queries with `orderBy('createdAt', 'desc')` fail
**Error:** "The query requires an index"

**Solution:**
```bash
# Create the index manually in Firebase Console:
# 1. Go to Firebase Console > Firestore > Indexes
# 2. Click "Create Index"
# 3. Collection: questions
# 4. Field: createdAt, Order: Descending
# 5. Query scope: Collection
```

### 2. **Missing or Invalid createdAt Fields**
**Problem:** Some documents don't have `createdAt` field
**Solution:** Run the diagnostic script to fix

### 3. **Inconsistent Data Structure**
**Problem:** Documents missing required fields
**Solution:** Use the data migration script below

## 🧪 **Immediate Testing Steps**

### Step 1: Test Simple Endpoint (No Ordering)
```bash
# This should work regardless of indexes
curl "http://localhost:4000/api/posts/simple?limit=5"
```

### Step 2: Test Basic Pagination
```bash
# This might fail if createdAt index is missing
curl "http://localhost:4000/api/posts/basic?page=1&limit=5"
```

### Step 3: Run Diagnostic Script
```bash
node diagnose_firestore.js
```

## 🔧 **Quick Fixes**

### Fix 1: Create Missing Index
Add this to your `firestore.indexes.json`:
```json
{
  "indexes": [
    {
      "collectionGroup": "questions",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "createdAt",
          "order": "DESCENDING"
        }
      ]
    }
  ]
}
```

Then deploy:
```bash
firebase deploy --only firestore:indexes
```

### Fix 2: Data Migration Script
```javascript
// Run this in Firebase Functions or Admin SDK
const { admin, db } = require('./functions/config/firebaseConfig');

async function fixQuestionDocuments() {
    const questionsRef = db.collection('questions');
    const snapshot = await questionsRef.get();
    
    const batch = db.batch();
    let updateCount = 0;
    
    snapshot.forEach(doc => {
        const data = doc.data();
        const updates = {};
        
        // Add missing createdAt
        if (!data.createdAt) {
            updates.createdAt = admin.firestore.FieldValue.serverTimestamp();
        }
        
        // Add missing replies array
        if (!Array.isArray(data.replies)) {
            updates.replies = [];
        }
        
        // Add missing tags array
        if (!Array.isArray(data.tags)) {
            updates.tags = [];
        }
        
        // Add missing fields with defaults
        if (!data.title) updates.title = 'Untitled';
        if (!data.body) updates.body = '';
        if (!data.userId) updates.userId = 'unknown';
        
        if (Object.keys(updates).length > 0) {
            batch.update(doc.ref, updates);
            updateCount++;
        }
    });
    
    if (updateCount > 0) {
        await batch.commit();
        console.log(`Updated ${updateCount} documents`);
    }
}
```

## 📊 **Available Endpoints for Testing**

1. **Simple (No Ordering)** - Always works:
   ```
   GET /api/posts/simple?limit=10
   ```

2. **Basic Pagination** - Requires createdAt index:
   ```
   GET /api/posts/basic?page=1&limit=15
   ```

3. **Scalable Pagination** - Requires createdAt index:
   ```
   GET /api/posts?page=1&limit=15
   ```

4. **Cursor-based** - Most efficient:
   ```
   GET /api/posts/cursor?limit=15
   ```

## 🔍 **Debugging Steps**

### 1. Check Server Logs
```bash
# If using local development
npm run dev

# Check for errors like:
# "The query requires an index"
# "Cannot read property 'toDate' of undefined"
```

### 2. Test Individual Document
```javascript
// Test reading a single document
const doc = await db.collection('questions').doc('YOUR_DOC_ID').get();
console.log(doc.data());
```

### 3. Test Simple Query
```javascript
// Test without ordering
const snapshot = await db.collection('questions').limit(1).get();
console.log(snapshot.docs[0].data());
```

## ✅ **Expected Document Structure**

Each question document should have:
```json
{
  "userId": "string",
  "title": "string (max 150 chars)",
  "body": "string",
  "tags": ["array", "of", "strings"],
  "createdAt": "Firestore Timestamp",
  "replies": []
}
```

## 🚀 **Next Steps**

1. **Start with simple endpoint**: `/api/posts/simple`
2. **Create the createdAt index** in Firebase Console
3. **Run data migration** to fix existing documents
4. **Test other endpoints** once index is ready

## 📞 **Still Having Issues?**

If problems persist:
1. Check Firebase Console > Firestore > Usage tab for errors
2. Verify Firestore rules allow read access
3. Check network connectivity
4. Ensure Firebase project is active and billing is enabled
