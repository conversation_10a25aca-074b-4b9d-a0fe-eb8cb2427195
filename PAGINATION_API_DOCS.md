# Forum Posts Pagination API Documentation

This document describes the paginated API endpoints for fetching forum posts from the Firestore `questions` collection.

## Available Endpoints

### 1. Basic Pagination (Small Datasets)
**Endpoint:** `GET /api/posts/basic`

Uses `get()` + `.slice()` approach. Suitable for small datasets (< 1000 posts).

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of posts per page (default: 15, max: 100)

**Example Request:**
```
GET /api/posts/basic?page=2&limit=10
```

**Response:**
```json
{
  "posts": [
    {
      "id": "doc_id_123",
      "userId": "user_123",
      "title": "Sample Question",
      "body": "Question content...",
      "tags": ["javascript", "firebase"],
      "createdAt": "2024-01-15T10:30:00Z",
      "replies": [],
      "replyCount": 0
    }
  ],
  "hasMore": true,
  "totalCount": 45,
  "currentPage": 2,
  "totalPages": 5
}
```

### 2. Scalable Pagination (Large Datasets)
**Endpoint:** `GET /api/posts` or `GET /api/posts/get`

Uses `startAfter()` with skip logic. Better for larger datasets.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of posts per page (default: 15, max: 100)

**Example Request:**
```
GET /api/posts?page=3&limit=15
```

**Response:**
```json
{
  "posts": [
    {
      "id": "doc_id_456",
      "userId": "user_456",
      "title": "Another Question",
      "body": "More content...",
      "tags": ["react", "nodejs"],
      "createdAt": "2024-01-14T15:45:00Z",
      "replies": [],
      "replyCount": 2
    }
  ],
  "hasMore": false
}
```

### 3. Cursor-Based Pagination (Most Scalable)
**Endpoint:** `GET /api/posts/cursor`

Uses cursor-based pagination with document IDs. Most efficient for very large datasets.

**Query Parameters:**
- `limit` (optional): Number of posts per page (default: 15, max: 100)
- `cursor` (optional): Document ID to start after (for subsequent pages)

**Example Requests:**
```
# First page
GET /api/posts/cursor?limit=10

# Subsequent pages
GET /api/posts/cursor?limit=10&cursor=doc_id_from_previous_response
```

**Response:**
```json
{
  "posts": [
    {
      "id": "doc_id_789",
      "userId": "user_789",
      "title": "Cursor Example",
      "body": "Content here...",
      "tags": ["pagination"],
      "createdAt": "2024-01-13T09:20:00Z",
      "replies": [],
      "replyCount": 1
    }
  ],
  "hasMore": true,
  "nextCursor": "doc_id_789"
}
```

## Data Structure

Each post object contains:
- `id`: Document ID
- `userId`: Author's user ID
- `title`: Post title (max 150 characters)
- `body`: Post content
- `tags`: Array of tags (max 5)
- `createdAt`: Timestamp when post was created
- `replies`: Array of replies (stored inline)
- `replyCount`: Calculated number of replies

## Sorting

All endpoints return posts sorted by `createdAt` in descending order (newest first).

## Error Responses

**400 Bad Request:**
```json
{
  "error": "Page must be greater than 0"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Error message details"
}
```

## Performance Recommendations

1. **Small datasets (< 1000 posts):** Use `/basic` endpoint
2. **Medium datasets (1000-10000 posts):** Use `/` endpoint
3. **Large datasets (> 10000 posts):** Use `/cursor` endpoint

## Frontend Implementation Examples

### Basic Pagination
```javascript
const fetchPosts = async (page = 1, limit = 15) => {
  const response = await fetch(`/api/posts/basic?page=${page}&limit=${limit}`);
  return response.json();
};
```

### Cursor-Based Pagination
```javascript
const fetchPostsCursor = async (limit = 15, cursor = null) => {
  const url = cursor 
    ? `/api/posts/cursor?limit=${limit}&cursor=${cursor}`
    : `/api/posts/cursor?limit=${limit}`;
  
  const response = await fetch(url);
  return response.json();
};
```

## Database Indexes Required

Ensure you have a Firestore index for:
- Collection: `questions`
- Fields: `createdAt` (Descending)

This index should already exist based on your current queries.
