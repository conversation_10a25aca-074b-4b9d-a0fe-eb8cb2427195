const fetch = require('node-fetch');

// Replace these with actual values from your database
const postId = 'REPLACE_WITH_ACTUAL_POST_ID';
const userId = 'REPLACE_WITH_ACTUAL_USER_ID';

async function testReplyApi() {
  try {
    console.log('Testing reply API...');
    
    // Test the test endpoint
    console.log('\nTesting /test endpoint...');
    const testResponse = await fetch(`http://localhost:4000/api/posts/${postId}/replies/test`);
    const testData = await testResponse.json();
    console.log('Test endpoint response:', testData);
    
    // Test creating a reply
    console.log('\nTesting POST endpoint...');
    const createResponse = await fetch(`http://localhost:4000/api/posts/${postId}/replies`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        content: 'This is a test reply from the test script',
        userId: userId
      })
    });
    
    const createData = await createResponse.json();
    console.log('Create reply response:', {
      status: createResponse.status,
      data: createData
    });
    
    // Test getting replies
    console.log('\nTesting GET endpoint...');
    const getResponse = await fetch(`http://localhost:4000/api/posts/${postId}/replies`);
    const getReplies = await getResponse.json();
    console.log('Get replies response:', {
      status: getResponse.status,
      count: Array.isArray(getReplies) ? getReplies.length : 'Not an array',
      data: getReplies
    });
    
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testReplyApi();
